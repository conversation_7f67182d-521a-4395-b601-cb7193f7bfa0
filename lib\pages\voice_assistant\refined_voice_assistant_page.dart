import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:speech_to_text/speech_to_text.dart';
import 'package:audioplayers/audioplayers.dart';
import '../../constants/app_theme.dart';
import '../../services/supabase_service.dart';
import '../../models/trip_model.dart';
import '../../widgets/trip_card.dart';
import '../trip/trip_details_page.dart';
import '../../utils/navigation_utils.dart';

class RefinedVoiceAssistantPage extends StatefulWidget {
  const RefinedVoiceAssistantPage({super.key});

  @override
  State<RefinedVoiceAssistantPage> createState() =>
      _RefinedVoiceAssistantPageState();
}

class _RefinedVoiceAssistantPageState extends State<RefinedVoiceAssistantPage>
    with TickerProviderStateMixin {
  // Voice recognition
  final SpeechToText _speechToText = SpeechToText();
  bool _speechEnabled = false;
  bool _isListening = false;
  String _recognizedText = '';

  // Audio feedback
  final AudioPlayer _audioPlayer = AudioPlayer();

  // Trip search
  List<TripModel> _foundTrips = [];
  bool _isSearching = false;
  String _statusMessage = 'مرحبا والف مرحبا، فين بغيت تمشي؟';

  // Animation controllers
  late AnimationController _rippleController;
  late AnimationController _pulseController;
  late AnimationController _glowController;

  // Animations
  late Animation<double> _rippleAnimation;
  late Animation<double> _pulseAnimation;
  late Animation<double> _glowAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _initializeSpeech();
  }

  void _initializeAnimations() {
    // Ripple animation for listening state
    _rippleController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    _rippleAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _rippleController,
      curve: Curves.easeOut,
    ));

    // Pulse animation for idle state
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );
    _pulseAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));

    // Glow animation for voice level
    _glowController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _glowAnimation = Tween<double>(
      begin: 0.3,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _glowController,
      curve: Curves.easeInOut,
    ));

    // Start idle animation
    _startIdleAnimation();
  }

  void _startIdleAnimation() {
    if (!_isListening) {
      _pulseController.repeat(reverse: true);
      _glowController.animateTo(0.4);
    }
  }

  void _startListeningAnimation() {
    _pulseController.stop();
    _rippleController.repeat();
    _glowController.animateTo(1.0);
  }

  void _stopAllAnimations() {
    _rippleController.stop();
    _pulseController.stop();
    _glowController.animateTo(0.4);
  }

  Future<void> _initializeSpeech() async {
    try {
      _speechEnabled = await _speechToText.initialize(
        onError: (error) {
          if (kDebugMode) {
            print('Speech recognition error: $error');
          }
          _onSpeechError(error.errorMsg);
        },
        onStatus: (status) {
          if (kDebugMode) {
            print('Speech recognition status: $status');
          }
          if (status == 'done' || status == 'notListening') {
            _stopListening();
          }
        },
      );

      if (_speechEnabled) {
        setState(() {
          _statusMessage = 'مرحبا والف مرحبا، فين بغيت تمشي؟';
        });
      } else {
        setState(() {
          _statusMessage = kIsWeb
              ? 'فشل في الوصول للميكروفون. تحقق من إعدادات المتصفح'
              : 'فشل في تهيئة التعرف على الصوت';
        });
      }
    } catch (e) {
      setState(() {
        _statusMessage = kIsWeb
            ? 'فشل في الوصول للميكروفون. تحقق من إعدادات المتصفح'
            : 'فشل في تهيئة التعرف على الصوت';
      });
    }
  }

  void _onSpeechError(String error) {
    if (mounted) {
      setState(() {
        _isListening = false;
        _statusMessage = 'حدث خطأ في التعرف على الصوت. حاول مرة أخرى';
      });
      _stopAllAnimations();
      _startIdleAnimation();
    }
  }

  Future<void> _startListening() async {
    if (!_speechEnabled || _isListening) return;

    setState(() {
      _isListening = true;
      _statusMessage = 'أستمع إليك...';
      _recognizedText = '';
      _foundTrips = []; // Clear previous results
    });

    _startListeningAnimation();
    await _playSound('audio/listening_start.mp3');

    try {
      await _speechToText.listen(
        onResult: (result) {
          if (mounted) {
            setState(() {
              _recognizedText = result.recognizedWords;
            });

            if (result.finalResult) {
              _processVoiceInput(result.recognizedWords);
            }
          }
        },
        listenFor: const Duration(seconds: 10),
        pauseFor: const Duration(seconds: 3),
        listenOptions: SpeechListenOptions(
          partialResults: true,
          onDevice: false,
          listenMode: ListenMode.confirmation,
        ),
        localeId: 'ar-MA', // Moroccan Arabic
        onSoundLevelChange: (level) {
          if (_isListening && mounted) {
            _glowController.animateTo(level.clamp(0.3, 1.0));
          }
        },
      );
    } catch (e) {
      _onSpeechError(e.toString());
    }
  }

  Future<void> _stopListening() async {
    if (!_isListening) return;

    setState(() {
      _isListening = false;
    });

    _stopAllAnimations();
    _startIdleAnimation();
    await _speechToText.stop();
    await _playSound('audio/listening_stop.mp3');
  }

  Future<void> _playSound(String assetPath) async {
    try {
      await _audioPlayer.stop();
      await _audioPlayer.play(AssetSource(assetPath));
    } catch (e) {
      // Fallback to haptic feedback
      await HapticFeedback.selectionClick();
    }
  }

  Future<void> _processVoiceInput(String text) async {
    if (text.trim().isEmpty) return;

    setState(() {
      _statusMessage = 'أفهم طلبك...';
      _isSearching = true;
      _foundTrips = [];
    });

    try {
      // Extract cities from the voice input
      final cities = _extractCities(text);

      if (cities.isEmpty) {
        setState(() {
          _statusMessage = 'لم أتمكن من فهم المدن المطلوبة. حاول مرة أخرى';
          _isSearching = false;
        });
        return;
      }

      // Search for trips
      final allTrips = await SupabaseService.getTrips();
      final filteredTrips = _filterTrips(allTrips, cities);

      setState(() {
        _foundTrips = filteredTrips;
        _isSearching = false;
      });

      if (filteredTrips.isNotEmpty) {
        setState(() {
          _statusMessage = 'آه لقيت ليك ${filteredTrips.length} رحلات، شوفها!';
        });
        await _playSound('audio/found_trip.mp3');
      } else {
        setState(() {
          _statusMessage = 'معليش، ما لقيتش رحلات دابا 😔';
        });
      }
    } catch (e) {
      setState(() {
        _statusMessage = 'حدث خطأ أثناء البحث';
        _isSearching = false;
      });
    }
  }

  List<String> _extractCities(String text) {
    // Moroccan cities in Arabic and French
    final cityMappings = {
      // Arabic names
      'الدار البيضاء': 'الدار البيضاء',
      'الرباط': 'الرباط',
      'فاس': 'فاس',
      'مراكش': 'مراكش',
      'أكادير': 'أكادير',
      'طنجة': 'طنجة',
      'مكناس': 'مكناس',
      'وجدة': 'وجدة',
      'تطوان': 'تطوان',
      'الجديدة': 'الجديدة',
      'القنيطرة': 'القنيطرة',
      'بني ملال': 'بني ملال',
      'الناظور': 'الناظور',
      'خريبكة': 'خريبكة',
      'سطات': 'سطات',

      // French names
      'casablanca': 'الدار البيضاء',
      'rabat': 'الرباط',
      'fes': 'فاس',
      'fez': 'فاس',
      'marrakech': 'مراكش',
      'marrakesh': 'مراكش',
      'agadir': 'أكادير',
      'tanger': 'طنجة',
      'tangier': 'طنجة',
      'meknes': 'مكناس',
      'oujda': 'وجدة',
      'tetouan': 'تطوان',
      'jadida': 'الجديدة',
      'kenitra': 'القنيطرة',
      'beni mellal': 'بني ملال',
      'nador': 'الناظور',
      'khouribga': 'خريبكة',
      'settat': 'سطات',
    };

    final foundCities = <String>[];
    final normalizedText = text.toLowerCase();

    for (final entry in cityMappings.entries) {
      if (normalizedText.contains(entry.key.toLowerCase())) {
        if (!foundCities.contains(entry.value)) {
          foundCities.add(entry.value);
        }
      }
    }

    return foundCities;
  }

  List<TripModel> _filterTrips(List<TripModel> trips, List<String> cities) {
    if (cities.isEmpty) return [];

    return trips.where((trip) {
      final fromCity = trip.fromCity.toLowerCase();
      final toCity = trip.toCity.toLowerCase();

      return cities.any((city) {
        final cityLower = city.toLowerCase();
        return fromCity.contains(cityLower) || toCity.contains(cityLower);
      });
    }).toList();
  }

  void _navigateToTripDetails(TripModel trip) {
    NavigationUtils.pushWithTransition(
      context,
      TripDetailsPage(tripId: trip.id),
      type: TransitionType.slide,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: const Text('المساعد الصوتي'),
        backgroundColor: AppColors.primary,
        foregroundColor: AppColors.textOnPrimary,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      body: SafeArea(
        child: Column(
          children: [
            // Main voice assistant interface
            Expanded(
              flex: 2,
              child: _buildVoiceInterface(),
            ),

            // Trip results section
            Expanded(
              flex: 3,
              child: _buildResultsSection(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildVoiceInterface() {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            AppColors.primary.withValues(alpha: 0.1),
            AppColors.background,
          ],
        ),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Status message
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 24),
            child: Text(
              _statusMessage,
              textAlign: TextAlign.center,
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    color: AppColors.textPrimary,
                    fontWeight: FontWeight.w600,
                  ),
            ),
          ),

          const SizedBox(height: 32),

          // Voice assistant circle with animations
          _buildVoiceCircle(),

          const SizedBox(height: 24),

          // Recognized text display
          if (_recognizedText.isNotEmpty) ...[
            Container(
              margin: const EdgeInsets.symmetric(horizontal: 24),
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: AppColors.surface,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: AppColors.border),
              ),
              child: Text(
                _recognizedText,
                textAlign: TextAlign.center,
                style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                      color: AppColors.textSecondary,
                    ),
              ),
            ),
            const SizedBox(height: 16),
          ],

          // Control buttons
          _buildControlButtons(),
        ],
      ),
    );
  }

  Widget _buildVoiceCircle() {
    return GestureDetector(
      onTap: _isListening ? _stopListening : _startListening,
      child: AnimatedBuilder(
        animation: Listenable.merge([
          _rippleAnimation,
          _pulseAnimation,
          _glowAnimation,
        ]),
        builder: (context, child) {
          return Stack(
            alignment: Alignment.center,
            children: [
              // Outer ripple effects (only when listening)
              if (_isListening) ...[
                for (int i = 0; i < 3; i++)
                  AnimatedBuilder(
                    animation: _rippleAnimation,
                    builder: (context, child) {
                      final delay = i * 0.3;
                      final animationValue =
                          (_rippleAnimation.value - delay).clamp(0.0, 1.0);

                      return Container(
                        width: 200 + (animationValue * 100),
                        height: 200 + (animationValue * 100),
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          border: Border.all(
                            color: AppColors.primary.withValues(
                              alpha: (1.0 - animationValue) * 0.3,
                            ),
                            width: 2,
                          ),
                        ),
                      );
                    },
                  ),
              ],

              // Main circle with glow
              AnimatedBuilder(
                animation: _isListening ? _glowAnimation : _pulseAnimation,
                builder: (context, child) {
                  final scale = _isListening ? 1.0 : _pulseAnimation.value;
                  final glowIntensity =
                      _isListening ? _glowAnimation.value : 0.4;

                  return Transform.scale(
                    scale: scale,
                    child: Container(
                      width: 120,
                      height: 120,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        gradient: RadialGradient(
                          colors: [
                            AppColors.primary.withValues(alpha: glowIntensity),
                            AppColors.primary,
                          ],
                        ),
                        boxShadow: [
                          BoxShadow(
                            color: AppColors.primary
                                .withValues(alpha: glowIntensity * 0.5),
                            blurRadius: 20 * glowIntensity,
                            spreadRadius: 5 * glowIntensity,
                          ),
                        ],
                      ),
                      child: Icon(
                        _isListening ? Icons.mic : Icons.mic_none,
                        size: 48,
                        color: AppColors.textOnPrimary,
                      ),
                    ),
                  );
                },
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildControlButtons() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        // Stop/Start button
        ElevatedButton.icon(
          onPressed: _speechEnabled
              ? (_isListening ? _stopListening : _startListening)
              : null,
          icon: Icon(_isListening ? Icons.stop : Icons.mic),
          label: Text(_isListening ? 'إيقاف' : 'تحدث'),
          style: ElevatedButton.styleFrom(
            backgroundColor: _isListening ? AppColors.error : AppColors.primary,
            foregroundColor: AppColors.textOnPrimary,
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(25),
            ),
          ),
        ),

        const SizedBox(width: 16),

        // Clear results button
        if (_foundTrips.isNotEmpty)
          OutlinedButton.icon(
            onPressed: () {
              setState(() {
                _foundTrips = [];
                _recognizedText = '';
                _statusMessage = 'مرحبا والف مرحبا، فين بغيت تمشي؟';
              });
            },
            icon: const Icon(Icons.clear),
            label: const Text('مسح'),
            style: OutlinedButton.styleFrom(
              foregroundColor: AppColors.textSecondary,
              side: const BorderSide(color: AppColors.border),
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(25),
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildResultsSection() {
    return Container(
      width: double.infinity,
      decoration: const BoxDecoration(
        color: AppColors.background,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(24),
          topRight: Radius.circular(24),
        ),
      ),
      child: Column(
        children: [
          // Results header
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(20),
            decoration: const BoxDecoration(
              color: AppColors.surface,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(24),
                topRight: Radius.circular(24),
              ),
              boxShadow: [
                BoxShadow(
                  color: AppColors.shadowLight,
                  blurRadius: 8,
                  offset: Offset(0, 2),
                ),
              ],
            ),
            child: Row(
              children: [
                Icon(
                  _foundTrips.isNotEmpty ? Icons.directions_car : Icons.search,
                  color: AppColors.primary,
                  size: 24,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    _foundTrips.isNotEmpty
                        ? 'النتائج (${_foundTrips.length})'
                        : _isSearching
                            ? 'جاري البحث...'
                            : 'ابدأ البحث الصوتي',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: AppColors.textPrimary,
                        ),
                  ),
                ),
                if (_isSearching)
                  const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor:
                          AlwaysStoppedAnimation<Color>(AppColors.primary),
                    ),
                  ),
              ],
            ),
          ),

          // Results content
          Expanded(
            child: _buildResultsContent(),
          ),
        ],
      ),
    );
  }

  Widget _buildResultsContent() {
    if (_isSearching) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
            ),
            SizedBox(height: 16),
            Text(
              'جاري البحث عن الرحلات...',
              style: TextStyle(
                color: AppColors.textSecondary,
                fontSize: 16,
              ),
            ),
          ],
        ),
      );
    }

    if (_foundTrips.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.mic,
              size: 64,
              color: AppColors.textTertiary,
            ),
            const SizedBox(height: 16),
            Text(
              'اضغط على الميكروفون وقل وجهتك',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    color: AppColors.textSecondary,
                  ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              'مثال: "بغيت نمشي من الرباط لكازا"',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: AppColors.textTertiary,
                  ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _foundTrips.length,
      itemBuilder: (context, index) {
        final trip = _foundTrips[index];
        return Container(
          margin: const EdgeInsets.only(bottom: 16),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: AppColors.shadow,
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: TripCard(
            trip: trip,
            onTap: () => _navigateToTripDetails(trip),
          ),
        );
      },
    );
  }

  @override
  void dispose() {
    _rippleController.dispose();
    _pulseController.dispose();
    _glowController.dispose();
    _speechToText.stop();
    _audioPlayer.dispose();
    super.dispose();
  }
}
